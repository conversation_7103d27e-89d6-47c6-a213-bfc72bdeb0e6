import React, { useEffect, useState, useRef } from "react";
import { toast } from "react-toastify";

import * as interfaces from "interfaces";

import IconAvatar from "shared/IconAvatar";
import Button from "shared/Button";

import classes from "./AnswerItem.module.scss";
import { AnswersService, ClientService, FileService, NgpVanService } from "services";
import { useServiceContext } from "services/ServiceProvider";
import EditTranscription from "popups/EditTranscription";
import EditEndDate from "popups/EditEndDate";
import SavedList from "popups/SavedList";
import AnswerSavedListInfo from "components/AnswerSavedListInfo";
import useClickOutside from "hooks/useClickOutside.hook";
import AnswerVideoStatus from "../AnswerVideoStatus";
import useAnswerVideoStatus, {
  AnswerVideoStatusEnum,
} from "../AnswerVideoStatus/useAnswerVideoStatus";
import VideoUploadModal from "../VideoHelpers/VideoUpload/VideoUploadModal";
import ThumbnailSelectionModal from "../VideoHelpers/ThumbnailSelectionModal";
import PinIcon from "assets/icons/pin.svg";
import PinWhiteIcon from "assets/icons/pin-white.svg";
import { cn } from "utils/utils";
import moment from "moment";

/**
 * Represents an AnswerItem component.
 *
 * @component
 * @param {Object} props - The component props.
 * @param {interfaces.AnswerInterface} props.answer - The answer object.
 * @param {AnswersService} props.answersService - The answers service.
 * @param {Function} props.setAnswers - The function to set the answers.
 * @param {boolean} props.dropdownOpened - Indicates if the dropdown is opened.
 * @param {Function} props.setOpenedDropdownId - The function to set the opened dropdown ID.
 * @param {NgpVanService} props.ngpVanService - The NGP VAN service.
 * @param {interfaces.UserInterface} [props.user] - The user object.
 * @returns {JSX.Element} The AnswerItem component.
 */
export default function AnswerItem(props: {
  answer: interfaces.AnswerInterface;
  answersService: AnswersService;
  fileService?: FileService;
  clientService?: ClientService;
  setAnswers: any;
  dropdownOpened: boolean;
  setOpenedDropdownId: any;
  ngpVanService: NgpVanService;
  user?: interfaces.UserInterface;
}) {
  const dropdownRef = useRef(null);
  const { isOutside: isOutsideDropdown, setIsOutside: setIsOutsideDropdown } =
    useClickOutside(dropdownRef);

  const [guideOpen, setGuideOpen] = useState(false);
  const [addTranscriptionOpen, setAddTranscriptionOpen] = useState(false);
  const [endDateOpen, setEndDateOpen] = useState(false);
  const [savedListInfo, setSavedListInfo] = useState<any>({});
  const [loading, setLoading] = useState(false);
  const [videoOpened, setVideoOpened] = useState(false);
  const [isVideoModalOpened, setIsVideoModalOpened] = useState(false);
  const [isThumbnailModalOpened, setIsThumbnailModalOpened] = useState(false);
  const {
    answer,
    setAnswers,
    dropdownOpened,
    setOpenedDropdownId,
    answersService,
    ngpVanService,
    clientService,
    user,
  } = props;

  const { adminStatsService } = useServiceContext();

  let thumbnailStyle = {
    backgroundImage: videoOpened ? '' : `url(${answer.imageUrl})`,
    backgroundSize: 'contain',
    backgroundPosition: 'center',
  };

  const handleToggleSubtitleDisplay = () => {
    answer.showTranscribedSubtitles = !answer.showTranscribedSubtitles;
    adminStatsService?.trackEvent('Answers', answer.showTranscribedSubtitles ? 'enable_subtitles' : 'disable_subtitles');

    answersService?.updateAnswer(answer, () =>
      answersService.getAnswers(setAnswers)
    );
  }

  const handleRemoveClick = () => {
    adminStatsService?.trackEvent('Answers', 'remove_answer');
    answersService?.removeAnswer(answer.id, () =>
      answersService.getAnswers(setAnswers)
    );
  };
  const handleDownloadAnswer = () => {
    adminStatsService?.trackEvent('Answers', 'download_video');
    // Use video proxy to ensure proper content-type headers for QuickTime compatibility
    const proxyUrl = `/api/v1.0.0/video-proxy?url=${encodeURIComponent(answer.videoUrl)}`;
    window.location.href = proxyUrl;
  };
  const handleSendClick = () => {
    setLoading(true);
    adminStatsService?.trackEvent('Answers', 'send_answer');

    ngpVanService?.getAnswerExports(answer.id, (item: any) => {
      if (!item?.data) {
        toast.error(
          "Please make sure you upload a CSV of users."
        );
        return;
      }

      setLoading(false);

      if (item.data.entryCount === 0) {
        setGuideOpen(true);
        adminStatsService?.trackEvent('Answers', 'open_saved_list_guide');
      } else {
        setSavedListInfo(item.data);
        adminStatsService?.trackEvent('Answers', 'answer_sent');
      }
    });
    // .then((item: any) => {setLoading(false); item.data.entryCount === 0 ? setGuideOpen(true) : setSavedListInfo(item.data)})
  };

  const handleDropdownClick = () => {
    const newState = !dropdownOpened;
    setOpenedDropdownId(newState ? answer.id : null);
    adminStatsService?.trackEvent('Answers', newState ? 'open_dropdown' : 'close_dropdown');
  };

  useEffect(() => {
    if (isOutsideDropdown) {
      setOpenedDropdownId(null);
      setIsOutsideDropdown(false);
    }
  }, [isOutsideDropdown, setOpenedDropdownId, setIsOutsideDropdown]);

  const handleAnswerPinned = () => {
    const payload = {
      id: answer.id,
      clientId: answer.clientId,
      isPinned: !answer.isPinned,
    }

    answersService.updateAnswer(payload, () => answersService.getAnswers(setAnswers))
  };

  const status = useAnswerVideoStatus(answer, answersService);
  return (
    <div className={cn(classes.AnswerItem, "group")} data-answer-id={answer.id}>
      <div className={classes.thumbnail} style={thumbnailStyle}>
        {videoOpened ? (
          <video
            width="100%"
            height="auto"
            controls
            src={answer.videoUrl}
            autoPlay
            playsInline
            onEnded={() => setVideoOpened(false)}
          />
        ) : (
          <Button
            text=""
            iconText="play"
            callback={() => setVideoOpened(true)}
          />
        )}
      </div>
      <div className={classes.meta}>
        <div className={classes.user}>
          <IconAvatar
            imageUrl={answer.question.user?.imageUrl}
            categoryIcon={answer.question.category}
          />
          <span className={classes.name}>
            {
              answer.question.overridingName ||
              (!answer.question.user.firstName && !answer.question.user.lastName ?
                'Anonymous' :
                answer.question.user.firstName + ' ' + answer.question.user.lastName)
            }
          </span>
        </div>

        {status === AnswerVideoStatusEnum.published && (
          <div onClick={handleAnswerPinned} className={cn(
            classes.pinButton,
            !answer.isPinned && classes.unpinned
          )}>
            <img src={answer.isPinned ? PinWhiteIcon : PinIcon} alt="Pin icon" />
          </div>
        )}

        {status === AnswerVideoStatusEnum.unpublished && (
          <div className={classes.buttonBar}>
            <Button text="Remove" callback={handleRemoveClick} />
            <Button
              text="Publish"
              callback={() => setIsVideoModalOpened(true)}
            />
            <Button
              text=""
              iconText={dropdownOpened ? "caret-up" : "caret-down"}
              callback={handleDropdownClick}
            />
            {dropdownOpened && (
              <div ref={dropdownRef} className={classes.dropdownBar}>
                <Button
                  text="Set the End Date"
                  iconText="calendar"
                  callback={() => {
                    setEndDateOpen(true);
                    adminStatsService?.trackEvent('Answers', 'set_end_date');
                  }}
                />
                <Button
                  text="Edit Subtitles"
                  iconText="font"
                  callback={() => {
                    setAddTranscriptionOpen(true);
                    adminStatsService?.trackEvent('Answers', 'edit_subtitles');
                  }}
                />
                <Button
                  text="Select Thumbnail"
                  iconText="camera"
                  callback={() => {
                    setIsThumbnailModalOpened(true);
                    adminStatsService?.trackEvent('Answers', 'select_thumbnail');
                  }}
                />
                <div className={classes.checkBoxButton}>
                  <input type="checkbox" checked={answer.showTranscribedSubtitles} onChange={handleToggleSubtitleDisplay} /> Enable Subtitles
                </div>
              </div>
            )}
          </div>
        )}

        {status === AnswerVideoStatusEnum.processing && (
          <div className={classes.buttonBar}>
            <Button text="Remove" callback={handleRemoveClick} />
            <Button
              text="View Details"
              callback={() => setIsVideoModalOpened(true)}
            />
          </div>
        )}

        {isVideoModalOpened && (
          <VideoUploadModal
            onClose={() => {
              setIsVideoModalOpened(false);
              answersService.getAnswers(setAnswers).catch(console.error);
            }}
            question={answer.question}
            answer={answer}
            clientService={clientService}
          />
        )}
        {isThumbnailModalOpened && (
          <ThumbnailSelectionModal
            onClose={() => {
              setIsThumbnailModalOpened(false);
              answersService.getAnswers(setAnswers).catch(console.error);
            }}
            question={answer.question}
            answer={answer}
          />
        )}
        {status === AnswerVideoStatusEnum.published && (
          <div className={classes.buttonBar}>
            <Button text="Remove" callback={handleRemoveClick} />
            <Button
              text={
                savedListInfo && savedListInfo.entryCount > 0
                  ? "Sent!"
                  : loading
                    ? "Loading..."
                    : "Send"
              }
              customClass={loading ? "primary" : ""}
              callback={handleSendClick}
            />
            {/* {wasSent ? 'Resend' : 'Send'} */}
            {/* <div className="answer-saved-list-percentage"></div> */}
            <Button
              text=""
              iconText={dropdownOpened ? "caret-up" : "caret-down"}
              callback={handleDropdownClick}
            />
            {dropdownOpened && (
              <div ref={dropdownRef} className={classes.dropdownBar}>
                <Button
                  text="Download Video"
                  iconText="download"
                  callback={handleDownloadAnswer}
                />
                <Button
                  text="Select Thumbnail"
                  iconText="camera"
                  callback={() => {
                    setIsThumbnailModalOpened(true);
                    adminStatsService?.trackEvent('Answers', 'select_thumbnail');
                  }}
                />
                <Button
                  text="Set the End Date"
                  iconText="calendar"
                  callback={() => {
                    setEndDateOpen(true);
                    adminStatsService?.trackEvent('Answers', 'set_end_date');
                  }}
                />
                <Button
                  text="Edit Subtitles"
                  iconText="font"
                  callback={() => {
                    setAddTranscriptionOpen(true);
                    adminStatsService?.trackEvent('Answers', 'edit_subtitles');
                  }}
                />
                <div className={classes.checkBoxButton}>
                  <input type="checkbox" checked={answer.showTranscribedSubtitles} onChange={handleToggleSubtitleDisplay} /> Enable Subtitles
                </div>
              </div>
            )}
          </div>
        )}

        <div className="w-full">
          <AnswerVideoStatus status={status} />
        </div>

        <div className={classes.question}>
          <div className={classes.questionText}>{answer.question.text}</div>
          <div className={classes.attributes}>
            <div>
              <span className={classes.votes}>{typeof answer.votes === 'object' ? answer.votes?.length || 0 : answer.votes || 0} votes</span>
              <span className={classes.time}>
                &nbsp; &bull; {moment(answer.createdAt).format("MMM D")}
              </span>
            </div>

            {answer.endDate && (
              <div>
                End Date: {(new Date(answer.endDate)).toLocaleDateString()}
              </div>
            )}
          </div>
          {savedListInfo.entryCount > 0 && (
            <AnswerSavedListInfo savedListInfo={savedListInfo} />
          )}
        </div>

        <SavedList
          isOpen={guideOpen}
          setSavedListInfo={setSavedListInfo}
          handleClose={() => setGuideOpen(false)}
          ngpVanService={ngpVanService}
          answerId={answer.id}
          user={user}
        />

        {addTranscriptionOpen && (
          <EditTranscription
            handleClose={() => setAddTranscriptionOpen(false)}
            transcription={answer.transcription || ""}
            answerId={answer.id}
            clientId={answer.clientId}
            answersService={answersService}
            setAnswers={setAnswers}
          />
        )}

        {endDateOpen && (
          <EditEndDate
            handleClose={() => setEndDateOpen(false)}
            answer={answer}
            answersService={answersService}
            setAnswers={setAnswers}
          />
        )}
      </div>
    </div>
  );
}
