import fs from "fs";
import path from "path";
import logger from "./utils/logger";
import AWS from "aws-sdk";

const S3 = new AWS.S3();

/**
 * Get content type based on file extension
 * @param {string} filePath - File path or key
 * @returns {string} Content type
 */
function getContentType(filePath) {
  const extension = path.extname(filePath).toLowerCase();

  const contentTypes = {
    // Video formats
    '.mp4': 'video/mp4',
    '.mov': 'video/quicktime',
    '.avi': 'video/x-msvideo',
    '.webm': 'video/webm',
    '.ogv': 'video/ogg',
    '.ogg': 'video/ogg',
    '.mpeg': 'video/mpeg',
    '.mpg': 'video/mpeg',
    '.wmv': 'video/x-ms-wmv',
    '.flv': 'video/x-flv',

    // Image formats
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.png': 'image/png',
    '.gif': 'image/gif',
    '.webp': 'image/webp',
    '.svg': 'image/svg+xml',
    '.bmp': 'image/bmp',

    // Audio formats
    '.mp3': 'audio/mpeg',
    '.wav': 'audio/wav',
    '.ogg': 'audio/ogg',
    '.aac': 'audio/aac',

    // Document formats
    '.pdf': 'application/pdf',
    '.doc': 'application/msword',
    '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    '.txt': 'text/plain',
    '.csv': 'text/csv'
  };

  return contentTypes[extension] || 'application/octet-stream';
}

export default async function uploadFileToS3({
  targetFileKey,
  file,
  filePath,
  targetBucket,
}) {
  logger.info(`Start of s3 upload process for ${filePath || targetFileKey}`);
  try {
    const outputData = file || (await fs.promises.readFile(filePath));

    logger.info("File read into outputData for S3 putParams");

    // Determine content type from the target file key or file path
    const contentType = getContentType(targetFileKey || filePath);
    logger.info(`Setting content type to: ${contentType} for file: ${targetFileKey}`);

    const putParams = {
      ACL: "public-read",
      Bucket: targetBucket,
      Key: targetFileKey,
      Body: outputData,
      ContentType: contentType,
    };

    const result = await S3.putObject(putParams).promise();

    logger.info(`Successfully uploaded file ${result.Key} to S3 with content type ${contentType}`);
  } catch (e) {
    logger.error("Error uploading file to S3:", JSON.stringify(e));
    throw e; // Re-throw to allow calling code to handle the error
  }
}
