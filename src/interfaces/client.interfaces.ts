export interface ClientInterface {
    id: string;
    enabled: boolean;
    name: string;
    email: string;
    isLocked: boolean;
    clientType: string;
    // isMfaRequired: boolean;
    link: string;
    logoURL: string;
    whiteLogoURL: string;
    expandedWhiteLogoURL: string;
    websiteURL: string;
    donationURL: string;
    volunteerURL: string;
    videoBannerURL: string;
    customPagePath: string;
    topBarColour: string;
    videoLinksColour: string;
    plusAskPillColour: string;
    newQuestionColour: string;
    openQuestionsAndAnswersColour: string;
    embedPrimaryColour: string;
    embedAccentColour: string;
    embedProgressBarColour: string;
    embedButtonColour: string;
    ngpVanUsername: string;
    ngpVanApiKey: string;
    categories: string;
    isPublished: boolean;
    donationDisplaysPermanently: boolean;
    campaignAddress: string;
    campaignName: string;
    topMessage: string;
    subject: string;
    donateText: string;
    donateCtaText: string;
    headerHomeLinkText: string;
    headerDonateLinkText: string;
    headerVolunteerLinkText: string;
    emailDonateCtaText: string;
    postQuestionText: string;
    postQuestionBtnText: string;
    textOptions: TextOptionsInterface;
    polymorphicOrgId: string;
    aiQuestionsEnabled: boolean;
    aiAskQuestionAlwaysOn: boolean;
    createdAt: string;
    updatedAt: string;
    state: string;
    populationSize: number;
}

export interface TextOptionsInterface {
    postVideoSurveyQuestion?: string;
    postVideoSurveyLessLikely?: string;
    postVideoSurveyNeutral?: string;
    postVideoSurveyMoreLikely?: string;
    questionExpectancy?: string;

    aiPromptDesktop?: string;
    aiPromptMobile?: string;
    aiDefaultQuestion1?: string;
    aiDefaultQuestion2?: string;
    aiDefaultQuestion3?: string;
    embedTitleText?: string;
}

export interface NewClientInterface {
    firstName: string;
    lastName: string;
    email: string;
    clientName: string;
    clientType: string;
}

export interface ClientEmailInfoInterface {
    fromEmailName: string;
    fromEmail: string;
    subject: string;
    topMessage: string;
    campaignName: string;
    campaignAddress: string;
}

export interface AvailableClientInterface {
    id: string;
    name: string;
    logoUrl: string;
    primaryColor: string;
    accessLevel: string;
    isPrimary: boolean;
}
