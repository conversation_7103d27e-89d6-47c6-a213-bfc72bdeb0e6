import axios from "axios";
import * as interfaces from 'interfaces';

import { ApiService } from './api.service'

export class AuthService extends ApiService {
    id: string
    public token?: string
    public session?: interfaces.SessionInterface | null
    public user?: interfaces.UserInterface | null
    public availableClients?: interfaces.ClientInterface[]

    constructor(token?: string) {
        super(token)
        this.id = 'AuthService'

        if (token) this.token = token

        // Add Axios interceptor
        axios.interceptors.response.use(
            response => response,
            error => {
                if (error.response && [401, 500].includes(error.response.status)) {
                    localStorage.clear();
                    // Redirect to login page
                    // window.location.href = '/';
                    window.location.reload();
                }
                return Promise.reject(error);
            }
        );
    }

    async login(email: string, password: string, callback: VoidFunction | any) {
        const endpoint = `${this.base}${this.paths.sessions}`;
        const errorIdentifier = `${this.id} -> Login`;
        const data = { email, password };

        await axios.post(endpoint, data).then((response: any) => {
            const responseData: interfaces.SessionInterface = response.data.data[0]

            this.session = responseData;

            if (this.session.loginLinkSent)
                return callback(this.session);

            if (!responseData.token || !responseData.user)
                this.reportInvalidResponseError(errorIdentifier, response);

            this.token = this.session?.token;
            this.user = this.session?.user;

            if (this.user) this.setlocalAuthState();

            return callback(this.session);
        })
            .catch(error => {
                this.reportRequestError(errorIdentifier, error);
                return callback(null);
            });

        return this.session;
    }

    async getCurrent(token: string, callback: VoidFunction | any) {
        const endpoint = `${this.base}${this.paths.currentSession}`;
        const errorIdentifier = `${this.id} -> GetCurrent`;

        const headers: Record<string, string> = {
            'Authorization': `${token}`
        };

        await axios.get(endpoint, { headers }).then((response: any) => {
            const responseData: interfaces.SessionInterface = response.data.data[0]

            if (!responseData.token || !responseData.user)
                this.reportInvalidResponseError(errorIdentifier, response);

            this.session = responseData;
            this.token = this.session?.token;
            this.user = this.session?.user;
            this.availableClients = this.session?.clients;

            if (this.user) this.setlocalAuthState();

            return callback(this.session);
        })

        return this.session;
    }

    public getLocalUser(): interfaces.UserInterface {
        const user = localStorage.getItem('user') || ''

        return JSON.parse(user);
    }

    public getToken(): string {
        return localStorage.getItem('token') || '';
    }

    setlocalAuthState() {
        if (this.token) localStorage.setItem('token', this.token);
        if (this.user) localStorage.setItem('user', JSON.stringify(this.user));

        // Store available clients if they exist
        if (this.availableClients) {
            localStorage.setItem('availableClients', JSON.stringify(this.availableClients));
        }

        // Determine which client to set as current
        const selectedClientId = localStorage.getItem('selectedClientId');
        const selectedClient = selectedClientId && this.availableClients?.find(client => client.id === selectedClientId);
        const clientToSet = selectedClient || this.user?.client;

        if (clientToSet) {
            localStorage.setItem('client', JSON.stringify(clientToSet));
        }
    }

    setSelectedClientId(clientId: string) {
        localStorage.setItem('selectedClientId', clientId);
        // Refresh the local auth state to update the client in localStorage
        this.setlocalAuthState();
    }

    getSelectedClientId(): string | null {
        return localStorage.getItem('selectedClientId');
    }

    async getAvailableClients(callback: (clients: interfaces.ClientInterface[]) => void) {
        // If we already have available clients from the session, use those
        if (this.availableClients && this.availableClients.length > 0) {
            return callback(this.availableClients);
        }

        // Otherwise, try to get them from localStorage
        const storedClients = localStorage.getItem('availableClients');
        if (storedClients) {
            try {
                const parsedClients = JSON.parse(storedClients);
                this.availableClients = parsedClients;
                return callback(parsedClients);
            } catch (error) {
                console.warn('Failed to parse stored available clients:', error);
            }
        }

        // Return empty array if no clients available
        return callback([]);
    }



    logOut() {
        localStorage.clear();
        if (window.location.href !== '/') window.location.href = '/';
        else window.location.reload()
    }
}
