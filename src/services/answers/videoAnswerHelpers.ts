import { logger } from "@/services/logger";
import { Answer, AnswerAttributes, AnswerModel } from "@/store/models/external";
import { selectBestAnswerWithAI } from "./aiSelectionHelpers";

const STOP_WORDS = [
  "the",
  "a",
  "an",
  "and",
  "or",
  "but",
  "in",
  "on",
  "at",
  "to",
  "for",
  "of",
  "with",
  "by",
  "is",
  "are",
  "was",
  "were",
  "be",
  "been",
  "being",
  "have",
  "has",
  "had",
  "do",
  "does",
  "did",
  "will",
  "would",
  "could",
  "should",
  "may",
  "might",
  "must",
  "can",
  "this",
  "that",
  "these",
  "those",
  "i",
  "you",
  "he",
  "she",
  "it",
  "we",
  "they",
  "me",
  "him",
  "her",
  "us",
  "them",
];

const MAX_KEYWORD_CANDIDATES = 5;

/**
 * Calculates word overlap between question and transcription text
 * Returns a score between 0 and 1 representing the percentage of question words found in transcription
 */
export const calculateWordOverlap = (question: string, transcription: string): number => {
  if (!question || !transcription) return 0;

  const questionWords = question
    .toLowerCase()
    .replace(/[^\w\s]/g, " ")
    .split(/\s+/)
    .filter((word) => word.length > 2)
    .filter((word) => !STOP_WORDS.includes(word));

  if (questionWords.length === 0) return 0;

  const transcriptionText = transcription.toLowerCase().replace(/[^\w\s]/g, " ");
  const matchingWords = questionWords.filter((word) => transcriptionText.includes(word));
  const overlapScore = matchingWords.length / questionWords.length;

  return overlapScore;
};

interface VideoAnswer {
  id: string;
  question: { text: string };
  transcription: { items: any[] };
  transcriptionText: string;
  stats: { answeredAt: Date | string };
}

/**
 * Gets video answers from the database using Answer model
 */
export const getVideoAnswersFromDatabase = async (clientId: number): Promise<VideoAnswer[]> => {
  try {
    const results = await Answer.findAll({
      where: {
        clientId: clientId,
      },
      order: [["createdAt", "DESC"]],
      raw: true,
    });

    if (results.length === 0) {
      return [];
    }

    logger.info(`Found ${results.length} video answers in the database`);

    return results.map((row) => {
      const rawRow = row as AnswerModel; // Cast to any since raw: true returns plain objects

      // Extract transcription text from JSONB field
      let transcriptionText = "";
      if (rawRow.transcription) {
        if (typeof rawRow.transcription === "string") {
          transcriptionText = rawRow.transcription;
        } else if (rawRow.transcription.text) {
          transcriptionText = rawRow.transcription.text;
        }
      }

      return {
        id: rawRow.id.toString(),
        question: { text: "" }, // Answer model doesn't have question field, could be derived from questionId
        transcription: { items: [] },
        transcriptionText,
        stats: { answeredAt: rawRow.createdAt },
      };
    });
  } catch (error) {
    logger.error("Error getting video answers from database:", error);
    return [];
  }
};

interface MatchingAnswer {
  repd_answer_id: string;
  question: string;
  transcription: string;
  overlapScore: number;
  source_type: string;
  created_at: string;
}

/**
 * Finds matching answers using keyword matching and OpenAI selection
 */
export const findMatchingAnswers = async (question: string, clientId: number): Promise<MatchingAnswer[]> => {
  try {
    const videos = await getVideoAnswersFromDatabase(clientId);

    if (videos.length === 0) {
      logger.info(`No videos found for clientId: ${clientId}`);
      return [];
    }

    logger.info(`Found ${videos.length} total videos for keyword matching`);

    const videosWithScores = videos
      .map((video) => {
        const questionText = video.question.text || "";
        const transcriptionText = video.transcriptionText || "";

        const questionScore = questionText ? calculateWordOverlap(question, questionText) : 0;
        const transcriptionScore = transcriptionText ? calculateWordOverlap(question, transcriptionText) : 0;
        const overlapScore = Math.max(questionScore, transcriptionScore);

        logger.info(
          `Video ${video.id}: question_score=${questionScore.toFixed(3)}, transcription_score=${transcriptionScore.toFixed(3)}, final_score=${overlapScore.toFixed(3)}`,
        );

        return {
          repd_answer_id: video.id,
          question: questionText,
          transcription: transcriptionText,
          overlapScore,
          source_type: "answer",
          created_at:
            typeof video.stats.answeredAt === "string" ? video.stats.answeredAt : video.stats.answeredAt.toISOString(),
        };
      })
      .filter((video) => video.overlapScore > 0)
      .sort((a, b) => b.overlapScore - a.overlapScore)
      .slice(0, MAX_KEYWORD_CANDIDATES);

    logger.info(`Found ${videos.length} total videos, ${videosWithScores.length} with keyword overlap`);

    if (videosWithScores.length === 0) {
      return [];
    }

    videosWithScores.forEach((video, index) => {
      logger.info(
        `Candidate ${index + 1}: Video ${video.repd_answer_id}, overlap_score=${video.overlapScore.toFixed(3)}`,
      );
    });

    const selectedAnswer = await selectBestAnswerWithAI(question, videosWithScores);
    return selectedAnswer ? [selectedAnswer as MatchingAnswer] : [];
  } catch (error) {
    logger.error("Error finding matching answers:", error);
    return [];
  }
};
