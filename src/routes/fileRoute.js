import env from '../global/environment.js';
import requestLib from 'request';
const fileDocumentBucket = env.getProperty('aws.buckets.file_document');
const fileImageBucket = env.getProperty('aws.buckets.file_image');

import path from 'path';
import { Readable } from 'stream';

import { v4 as uuidv4 } from 'uuid'; // Updated import statement

import express from 'express';
const router = express.Router();

import errorUtil from '../utils/errorUtil.js';
import { __dirname } from '../utils/sysUtil.js';

import { s3 } from '../global/aws.js';

import {
	multerFileDocument, multerFileImage, testUpload,
	FILE_EXT_REGEX
} from '../services/fileService.js';

import timeout from 'connect-timeout';
// import invokeLambda from '../global/invokeLambda.js';

import { conditionalCelebrate } from '../helpers/validationHelper.js';

// const LOG_THRESHOLD = 20 * 1024 * 1024; // 50MB

// Routes

router.post( '/api/v1.0.0/files/documents', conditionalCelebrate(), timeout((60 * 25) + 's'), ( request, response, next ) => {
	return upload( request, response, multerFileDocument(), fileDocumentBucket );
} );

router.post( '/api/v1.0.0/upload-test', conditionalCelebrate(), timeout((60 * 25) + 's'), ( request, response, next ) => {
	return upload( request, response, testUpload(), 'test' );
} );

router.get( '/api/v1.0.0/video-proxy', ( request, response, next ) => {
	const videoUrl = request.query.url;

	if (!videoUrl) {
		return response.status(400).json({ error: 'Video URL is required' });
	}

	// Determine content type from URL extension
	const getContentTypeFromUrl = (url) => {
		const extension = path.extname(url.split('?')[0]).toLowerCase(); // Remove query params
		const contentTypes = {
			'.mp4': 'video/mp4',
			'.mov': 'video/quicktime',
			'.avi': 'video/x-msvideo',
			'.webm': 'video/webm',
			'.ogv': 'video/ogg',
			'.ogg': 'video/ogg',
			'.mpeg': 'video/mpeg',
			'.mpg': 'video/mpeg'
		};
		return contentTypes[extension] || 'video/mp4'; // Default to mp4
	};

	const contentType = getContentTypeFromUrl(videoUrl);

	// Set proper headers for video download
	response.setHeader('Content-Type', contentType);
	response.setHeader('Content-Disposition', 'attachment');
	response.setHeader('Cache-Control', 'public, max-age=31536000'); // Cache for 1 year

	// Pipe the video through with error handling
	const videoRequest = requestLib(videoUrl);

	videoRequest.on('error', (error) => {
		console.error('Error fetching video:', error);
		if (!response.headersSent) {
			response.status(500).json({ error: 'Failed to fetch video' });
		}
	});

	videoRequest.pipe(response);
} );

router.post( '/api/v1.0.0/files/images', conditionalCelebrate(), timeout((60 * 25) + 's'), ( request, response, next ) => {
	return upload( request, response, multerFileImage(), fileImageBucket );
} );

// private

function upload( request, response, multer, bucket ) {
	request.setTimeout(300000 * 3); // 15 minutes

	// Handle file upload - use simple upload for images, multipart for videos
	const handleFileUpload = async (fileStream, filename, fileBuffer = null) => {
		const uuid = uuidv4();
		const extension = path.extname(filename);

		// Determine if this is a video or image file
		const isVideo = filename.match(FILE_EXT_REGEX) !== null;
		const keyPrefix = isVideo ? 'video_parts' : 'files';
		const baseUrl = 'https://files.repd.us';

		// For images, use simple S3 upload
		if (!isVideo && fileBuffer) {
			// Determine content type from extension
			const getContentType = (ext) => {
				const contentTypes = {
					'.jpg': 'image/jpeg',
					'.jpeg': 'image/jpeg',
					'.png': 'image/png',
					'.gif': 'image/gif',
					'.webp': 'image/webp',
					'.svg': 'image/svg+xml',
					'.bmp': 'image/bmp'
				};
				return contentTypes[ext.toLowerCase()] || 'application/octet-stream';
			};

			const uploadParams = {
				Bucket: bucket,
				Key: `${keyPrefix}/${uuid}${extension}`,
				Body: fileBuffer,
				ACL: 'public-read',
				ContentType: getContentType(extension)
			};

			try {
				const result = await s3.upload(uploadParams).promise();
				return {
					'message': 'File uploaded.',
					'totalEntries': 1,
					'data': [{
						url: `${baseUrl}/${keyPrefix}/${uuid}${extension}`
					}]
				};
			} catch (error) {
				console.error('Simple upload error:', error);
				throw error;
			}
		}

		// For videos, use multipart upload
		const multipartParams = {
			Bucket: bucket,
			Key: `${keyPrefix}/${uuid}/original${extension}`,
			ACL: 'public-read'
		};
		
		try {
			// Create multipart upload
			const multipartUpload = await s3.createMultipartUpload(multipartParams).promise();
			const uploadId = multipartUpload.UploadId;
			
			const CHUNK_SIZE = 5 * 1024 * 1024; // 5MB minimum for S3 multipart
			let partNumber = 1;
			let buffer = Buffer.alloc(0);
			const parts = []; // Will store {PartNumber, ETag, Promise} objects

			console.log("Multipart params:", multipartParams, "Upload ID:", uploadId);
			
			// Process the stream in chunks
			return new Promise((resolve, reject) => {
				fileStream.on('data', (chunk) => {
					buffer = Buffer.concat([buffer, chunk]);
					
					while (buffer.length >= CHUNK_SIZE) {
						const partBuffer = buffer.slice(0, CHUNK_SIZE);
						buffer = buffer.slice(CHUNK_SIZE);
						
						const currentPartNumber = partNumber++;
						
						const partParams = {
							Body: partBuffer,
							Bucket: bucket,
							Key: `${keyPrefix}/${uuid}/original${extension}`,
							PartNumber: currentPartNumber,
							UploadId: uploadId
						};
						
						const uploadPromise = s3.uploadPart(partParams).promise()
							.then(data => {
								console.log(`Uploaded part ${currentPartNumber}`);
								return {
									PartNumber: currentPartNumber,
									ETag: data.ETag
								};
							});
						
						parts.push({
							PartNumber: currentPartNumber,
							promise: uploadPromise
						});
					}
				});
				
				fileStream.on('end', async () => {
					try {
						// Upload any remaining data
						if (buffer.length > 0) {
							const currentPartNumber = partNumber;
							
							const partParams = {
								Body: buffer,
								Bucket: bucket,
								Key: `${keyPrefix}/${uuid}/original${extension}`,
								PartNumber: currentPartNumber,
								UploadId: uploadId
							};
							
							const uploadPromise = s3.uploadPart(partParams).promise()
								.then(data => {
									console.log(`Uploaded final part ${currentPartNumber}`);
									return {
										PartNumber: currentPartNumber,
										ETag: data.ETag
									};
								});
							
							parts.push({
								PartNumber: currentPartNumber,
								promise: uploadPromise
							});
						}
						
						// Wait for all parts to upload and collect results
						const uploadResults = await Promise.all(parts.map(part => part.promise));
						
						// Sort parts by part number
						const sortedParts = uploadResults.sort((a, b) => a.PartNumber - b.PartNumber);
						
						console.log("Completing multipart upload with parts:", sortedParts);
						
						// Complete the multipart upload
						const completeParams = {
							Bucket: bucket,
							Key: `${keyPrefix}/${uuid}/original${extension}`,
							MultipartUpload: {
								Parts: sortedParts
							},
							UploadId: uploadId
						};
						
						await s3.completeMultipartUpload(completeParams).promise();

						// This should only be reached for video files
						resolve({
							'message': 'Video File Uploaded.',
							'totalEntries': 1,
							'data': [{
								original: `${baseUrl}/${keyPrefix}/${uuid}/original${extension}`,
								videoDuration: 0,
								mp4: `${baseUrl}/${keyPrefix}/${uuid}/converted.mp4`,
								webm: `${baseUrl}/${keyPrefix}/${uuid}/converted.webm`,
								ogv: `${baseUrl}/${keyPrefix}/${uuid}/converted.ogv`,
								videoKey: uuid + extension,
								uploadId: uploadId
							}]
						});
					} catch (error) {
						console.error('Error in multipart upload:', error);
						
						// Abort the multipart upload if there's an error
						// await s3.abortMultipartUpload({
						// 	Bucket: bucket,
						// 	Key: `video_parts/${uuid}/original${extension}`,
						// 	UploadId: uploadId
						// }).promise().catch(console.error);
						
						reject(error);
					}
				});
				
				fileStream.on('error', async (error) => {
					console.error('Stream error:', error);
					
					// Abort the multipart upload if there's an error
					// await s3.abortMultipartUpload({
					// 	Bucket: bucket,
					// 	Key: `video_parts/${uuid}/original${extension}`,
					// 	UploadId: uploadId
					// }).promise().catch(console.error);
					
					reject(error);
				});
			});
		} catch (error) {
			console.error('Error creating multipart upload:', error);
			throw error;
		}
	};

	multer.single('file')(request, response, async (error) => {
		if (error) {
			console.error('File Error', error);
			return handleError(request, response, multer, error);
		}

		if (bucket === 'test') {
			response.status(201).json('ok');
			return;
		}

		if (!request.file) {
			return response.status(400).json({
				error: 'Files with this content type cannot be uploaded.'
			});
		}

		try {
			// Convert buffer to stream for videos, pass buffer directly for images
			const fileStream = Readable.from(request.file.buffer);
			const result = await handleFileUpload(fileStream, request.file.originalname, request.file.buffer);

			// The result already has the correct format based on file type
			response.status(201).json(result);
		} catch (error) {
			console.error('Upload Error:', error);
			return errorUtil.sendErrorResponse(response, error);
		}
	});
}

function handleError( request, response, multer, error ) {
	console.error('File upload error:', error);

	// if ( error.code === 'LIMIT_FILE_SIZE' ) {
	// 	const maxSizeInKb = multer.limits.fileSize / 1000;
	// 	return response.status(400).json({
	// 		error: `File too large. Maximum size is ${maxSizeInKb}KB`
	// 	});
	// }

	if ( error.code === 'WRONG_FILE_TYPE' ) {
		return response.status(400).json({
			error: 'Invalid file type'
		});
	}

	return response.status(500).json({
		error: 'File upload failed',
		details: error.message
	});
}

export default {
	router: router,
	securityConstraints: [ {
		'regex': '/api/v.*/files',
		'methods': [ 'POST' ],
		'accessLevels': []
	} ]
};
